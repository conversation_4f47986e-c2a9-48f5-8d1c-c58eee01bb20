# Testing the Auth Login API with Firebase Session Cookies

## Manual Testing Steps

### 1. Test Google OAuth Login Flow

1. Navigate to `/login` or `/signup`
2. Click "Continue with Google"
3. Complete Google OAuth flow
4. Check browser dev tools:
   - **Application > Cookies**: Should see `firebase-auth-token` (HttpOnly session cookie) and `firebase-auth-token-client`
   - **Network tab**: Should see successful POST to `/api/auth/login`

### 2. Test Server-Side Authentication

1. After logging in, navigate to `/dashboard`
2. Check that the page loads without redirecting to login
3. Open browser dev tools > Network tab
4. Refresh the page - should see no auth errors in server logs

### 3. Test Logout Flow

1. While logged in, click logout
2. Check browser dev tools:
   - **Application > Cookies**: Both auth cookies should be cleared
   - **Network tab**: Should see POST to `/api/auth/logout`
3. Try accessing `/dashboard` - should redirect to login

### 4. Test API Authentication

1. While logged in, open browser dev tools > Console
2. Run this test:

```javascript
fetch("/api/weather?lat=40.7128&lon=-74.0060", {
  headers: {
    Authorization: `Bearer ${document.cookie.split("firebase-auth-token-client=")[1]?.split(";")[0]}`,
  },
})
  .then((r) => r.json())
  .then(console.log)
```

3. Should return weather data, not auth error

## Automated Testing (Future)

### Unit Tests for API Endpoints

```javascript
// Test login endpoint
describe("/api/auth/login", () => {
  it("should set HttpOnly cookie with valid token", async () => {
    // Mock Firebase Admin verifyIdToken
    // Send POST with valid idToken
    // Assert cookie is set in response headers
  })

  it("should return 401 with invalid token", async () => {
    // Send POST with invalid idToken
    // Assert 401 response
  })
})

// Test logout endpoint
describe("/api/auth/logout", () => {
  it("should clear auth cookie", async () => {
    // Send POST to logout
    // Assert cookie is cleared in response headers
  })
})
```

### Integration Tests

```javascript
describe("Auth Flow Integration", () => {
  it("should complete full login/logout cycle", async () => {
    // Mock Google OAuth
    // Test login API call
    // Test protected route access
    // Test logout API call
    // Test protected route denial
  })
})
```

## Common Issues to Check

1. **Cookie Domain**: Ensure cookies work across subdomains if needed
2. **HTTPS**: Secure flag should be set in production
3. **Token Expiration**: Test with expired Firebase tokens
4. **Middleware**: Verify protected routes are properly secured
5. **Server Components**: Check SSR auth works correctly

## Environment Variables Required

Ensure these are set:

```
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-auth-domain
```
