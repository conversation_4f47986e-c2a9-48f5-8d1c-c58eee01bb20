"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Plus, HelpCircle } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { formatDateRange } from "./utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { useEffect, useState } from "react"
import { UserService } from "@/lib/domains/user/user.service"
import { User } from "@/lib/domains/user/user.types"
import { SquadService } from "@/lib/domains/squad/squad.service"
import { SquadMember } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"

interface SquadsTabProps {
  squads: Squad[]
  upcomingTrips: Trip[]
  loading: boolean
  squadLeaders?: Record<string, User>
}

export function SquadsTab({
  squads,
  upcomingTrips,
  loading,
  squadLeaders: initialSquadLeaders = {},
}: SquadsTabProps) {
  const [squadLeaders, setSquadLeaders] = useState<Record<string, User>>(initialSquadLeaders)
  const [squadMembers, setSquadMembers] = useState<Record<string, User[]>>({})
  const [loadingMembers, setLoadingMembers] = useState<Record<string, boolean>>({})

  // Progressive loading: Fetch squad leader and member details with debouncing
  useEffect(() => {
    if (squads.length === 0) return

    // Debounce the data fetching to avoid excessive API calls
    const timeoutId = setTimeout(async () => {
      const newLeaders: Record<string, User> = {}
      const newMembers: Record<string, User[]> = {}
      const newLoadingStates: Record<string, boolean> = {}

      // Process squads in batches to avoid overwhelming the API
      const batchSize = 3
      for (let i = 0; i < squads.length; i += batchSize) {
        const squadBatch = squads.slice(i, i + batchSize)

        await Promise.allSettled(
          squadBatch.map(async (squad) => {
            // Fetch leader if not already cached
            if (squad.leaderId && !squadLeaders[squad.leaderId]) {
              try {
                const leader = await UserService.getUser(squad.leaderId)
                if (leader) {
                  newLeaders[squad.leaderId] = leader
                }
              } catch (error) {
                console.error(`Error fetching leader for squad ${squad.id}:`, error)
              }
            }

            // Fetch squad members if not already cached and not currently loading
            if (!squadMembers[squad.id] && !loadingMembers[squad.id]) {
              newLoadingStates[squad.id] = true

              try {
                const squadMembersList = await SquadService.getSquadMembers(squad.id)
                const memberUsers: User[] = []

                // Limit to first 3 members (excluding leader) for better performance
                const membersToFetch = squadMembersList
                  .filter((squadMember) => squadMember.userId !== squad.leaderId)
                  .slice(0, 3)

                // Fetch member details in parallel
                const memberPromises = membersToFetch.map(async (squadMember) => {
                  try {
                    return await UserService.getUser(squadMember.userId)
                  } catch (error) {
                    console.error(
                      `Error fetching member ${squadMember.userId} for squad ${squad.id}:`,
                      error
                    )
                    return null
                  }
                })

                const memberResults = await Promise.allSettled(memberPromises)
                memberResults.forEach((result) => {
                  if (result.status === "fulfilled" && result.value) {
                    memberUsers.push(result.value)
                  }
                })

                newMembers[squad.id] = memberUsers
                newLoadingStates[squad.id] = false
              } catch (error) {
                console.error(`Error fetching members for squad ${squad.id}:`, error)
                newMembers[squad.id] = []
                newLoadingStates[squad.id] = false
              }
            }
          })
        )

        // Update state after each batch to provide progressive loading
        if (Object.keys(newLeaders).length > 0) {
          setSquadLeaders((prev) => ({ ...prev, ...newLeaders }))
        }
        if (Object.keys(newMembers).length > 0) {
          setSquadMembers((prev) => ({ ...prev, ...newMembers }))
        }
        if (Object.keys(newLoadingStates).length > 0) {
          setLoadingMembers((prev) => ({ ...prev, ...newLoadingStates }))
        }
      }
    }, 100) // Small debounce delay

    return () => clearTimeout(timeoutId)
  }, [squads.length]) // Only depend on squads length to avoid excessive re-renders
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Your Squads</h2>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                aria-label="Squad information"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-3" align="start">
              <p className="text-sm">
                Squads aren't just for friend groups, add your partner, family, or travel buddies
                and make every trip easier to plan.
              </p>
            </PopoverContent>
          </Popover>
        </div>
        <Link href="/squads/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Create Squad
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {squads.map((squad) => (
          <Link href={`/squads/${squad.id}`} key={squad.id}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle>{squad.name}</CardTitle>
                <CardDescription>{squad.memberCount || 0} members</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex -space-x-2 overflow-hidden mb-4">
                  {/* Show squad leader first */}
                  {squad.leaderId && squadLeaders[squad.leaderId] ? (
                    <Avatar className="border-2 border-background">
                      <AvatarImage
                        src={getBestAvatar(
                          squadLeaders[squad.leaderId].photoURL,
                          squadLeaders[squad.leaderId].displayName,
                          40
                        )}
                        alt={squadLeaders[squad.leaderId].displayName || "Squad Leader"}
                      />
                      <AvatarFallback>
                        {getInitials(squadLeaders[squad.leaderId].displayName)}
                      </AvatarFallback>
                    </Avatar>
                  ) : squad.leaderId ? (
                    <Skeleton className="h-10 w-10 rounded-full border-2 border-background" />
                  ) : null}

                  {/* Show actual member avatars or loading skeletons */}
                  {loadingMembers[squad.id] ? (
                    // Show skeleton avatars while loading (up to 5 members)
                    [...Array(Math.min(5, Math.max(0, (squad.memberCount || 0) - 1)))].map(
                      (_, i) => (
                        <Skeleton
                          key={`skeleton-${i}`}
                          className="h-10 w-10 rounded-full border-2 border-background"
                        />
                      )
                    )
                  ) : (
                    <>
                      {/* Show actual member avatars (up to 5) */}
                      {squadMembers[squad.id]?.slice(0, 5).map((member, i) => (
                        <Avatar key={member.id || i} className="border-2 border-background">
                          <AvatarImage
                            src={getBestAvatar(member.photoURL, member.displayName, 40)}
                            alt={member.displayName || "Squad Member"}
                          />
                          <AvatarFallback>{getInitials(member.displayName)}</AvatarFallback>
                        </Avatar>
                      ))}
                      {/* Show placeholder avatars for remaining members if we don't have their data yet */}
                      {!squadMembers[squad.id] &&
                        [...Array(Math.min(5, Math.max(0, (squad.memberCount || 0) - 1)))].map(
                          (_, i) => (
                            <Avatar key={`placeholder-${i}`} className="border-2 border-background">
                              <AvatarFallback className="bg-muted text-muted-foreground">
                                {i + 2}
                              </AvatarFallback>
                            </Avatar>
                          )
                        )}
                    </>
                  )}

                  {/* Show +X indicator for additional members */}
                  {(squad.memberCount || 0) > 6 && (
                    <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-background bg-muted text-xs">
                      +{(squad.memberCount || 0) - 6}
                    </div>
                  )}
                </div>

                {/* Find upcoming trip for this squad */}
                {upcomingTrips.find((trip) => trip.squadId === squad.id) ? (
                  <div className="text-sm">
                    <Badge variant="outline" className="mb-2">
                      Upcoming Trip
                    </Badge>
                    <p className="font-medium">
                      {upcomingTrips.find((trip) => trip.squadId === squad.id)?.destination}
                    </p>
                    <p className="text-muted-foreground">
                      {formatDateRange(
                        upcomingTrips.find((trip) => trip.squadId === squad.id)?.startDate,
                        upcomingTrips.find((trip) => trip.squadId === squad.id)?.endDate
                      )}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No upcoming trips</p>
                )}
              </CardContent>
            </Card>
          </Link>
        ))}

        <Card className="h-full border-dashed">
          <CardContent className="flex flex-col items-center justify-center h-full p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Plus className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">Create a New Squad</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Add your spouse, college, or work friends.
            </p>
            <Link href="/squads/create" className="mt-4">
              <Button variant="outline">Get Started</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
