"use client"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface DashboardOptimizedImageProps {
  src: string
  alt: string
  className?: string
  priority?: boolean
  index?: number
  aspectRatio?: "video" | "square"
  quality?: number
  sizes?: string
}

/**
 * Optimized image component for dashboard with advanced loading strategies
 */
export function DashboardOptimizedImage({
  src,
  alt,
  className,
  priority = false,
  index = 0,
  aspectRatio = "video",
  quality = 75,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
}: DashboardOptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const imgRef = useRef<HTMLDivElement>(null)

  // Intersection observer for lazy loading below-the-fold images
  useEffect(() => {
    if (priority || index < 3) return // Skip for priority/above-the-fold images

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Image is about to be visible, start loading
            setIsLoading(false)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: "50px", // Start loading 50px before image is visible
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [priority, index])

  const handleLoad = () => {
    setIsLoading(false)
  }

  const handleError = () => {
    setHasError(true)
    setIsLoading(false)
  }

  // Determine if image should be loaded immediately
  const shouldLoadImmediately = priority || index < 3

  return (
    <div
      ref={imgRef}
      className={cn(
        "relative overflow-hidden",
        aspectRatio === "video" ? "aspect-video" : "aspect-square",
        className
      )}
    >
      {/* Loading skeleton */}
      {isLoading && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}

      {/* Error fallback */}
      {hasError && (
        <div className="absolute inset-0 bg-muted flex items-center justify-center">
          <div className="text-muted-foreground text-sm">Image unavailable</div>
        </div>
      )}

      {/* Actual image */}
      {(shouldLoadImmediately || !isLoading) && !hasError && (
        <Image
          src={src}
          alt={alt}
          fill
          className={cn(
            "object-cover transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100"
          )}
          priority={priority}
          quality={quality}
          sizes={sizes}
          onLoad={handleLoad}
          onError={handleError}
          // Use WebP format when possible
          unoptimized={false}
        />
      )}

      {/* Gradient overlay for text readability */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent h-1/2 pointer-events-none" />
    </div>
  )
}
