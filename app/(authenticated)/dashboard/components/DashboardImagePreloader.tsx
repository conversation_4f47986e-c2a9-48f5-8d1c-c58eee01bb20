"use client"

import { useEffect } from "react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"

interface DashboardImagePreloaderProps {
  upcomingTrips: Trip[]
  pastTrips: Trip[]
}

/**
 * Component to preload critical images for better LCP
 */
export function DashboardImagePreloader({ upcomingTrips, pastTrips }: DashboardImagePreloaderProps) {
  useEffect(() => {
    // Preload images for the first few trips (above the fold)
    const tripsToPreload = [...upcomingTrips.slice(0, 3), ...pastTrips.slice(0, 2)]
    
    tripsToPreload.forEach((trip, index) => {
      const imageUrl = getTripImageUrl(trip, "400x200")
      
      // Create link element for preloading
      const link = document.createElement("link")
      link.rel = "preload"
      link.as = "image"
      link.href = imageUrl
      
      // Add priority for first image (LCP candidate)
      if (index === 0) {
        link.setAttribute("fetchpriority", "high")
      }
      
      document.head.appendChild(link)
      
      // Clean up on unmount
      return () => {
        if (document.head.contains(link)) {
          document.head.removeChild(link)
        }
      }
    })
  }, [upcomingTrips, pastTrips])

  return null
}
