"use client"

import { useEffect } from "react"

interface DashboardPerformanceMonitorProps {
  hasInitialData: boolean
  hasRealtimeSync: boolean
}

/**
 * Component to monitor dashboard performance metrics
 */
export function DashboardPerformanceMonitor({
  hasInitialData,
  hasRealtimeSync,
}: DashboardPerformanceMonitorProps) {
  useEffect(() => {
    // Temporarily disabled due to TypeScript issues
    // TODO: Re-enable after fixing performance API types
    console.log("Dashboard Performance:", { hasInitialData, hasRealtimeSync })
    return

    // Measure dashboard-specific metrics
    /* const measureDashboardMetrics = () => {
      // Time to initial data render
      if (hasInitialData && !window.dashboardMetrics?.initialDataTime) {
        window.dashboardMetrics = window.dashboardMetrics || {}
        window.dashboardMetrics.initialDataTime = performance.now()

        // Report to analytics if available
        if (typeof window !== "undefined" && window.gtag) {
          window.gtag("event", "dashboard_initial_data", {
            event_category: "performance",
            value: Math.round(window.dashboardMetrics.initialDataTime),
          })
        }
      }

      // Time to realtime sync
      if (hasRealtimeSync && !window.dashboardMetrics?.realtimeSyncTime) {
        window.dashboardMetrics = window.dashboardMetrics || {}
        window.dashboardMetrics.realtimeSyncTime = performance.now()

        if (typeof window !== "undefined" && window.gtag) {
          window.gtag("event", "dashboard_realtime_sync", {
            event_category: "performance",
            value: Math.round(window.dashboardMetrics.realtimeSyncTime),
          })
        }
      }
    }

    measureDashboardMetrics()

    // Measure Core Web Vitals specific to dashboard
    const measureWebVitals = () => {
      // LCP (Largest Contentful Paint) - likely the first trip card image
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]

        if (typeof gtag !== "undefined") {
          gtag("event", "dashboard_lcp", {
            event_category: "web_vitals",
            value: Math.round(lastEntry.startTime),
          })
        }
      }).observe({ entryTypes: ["largest-contentful-paint"] })

      // CLS (Cumulative Layout Shift)
      let clsValue = 0
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        }

        if (typeof gtag !== "undefined") {
          gtag("event", "dashboard_cls", {
            event_category: "web_vitals",
            value: Math.round(clsValue * 1000),
          })
        }
      }).observe({ entryTypes: ["layout-shift"] })

      // FID (First Input Delay)
      new PerformanceObserver((list) => {
        const firstInput = list.getEntries()[0]

        if (typeof gtag !== "undefined") {
          gtag("event", "dashboard_fid", {
            event_category: "web_vitals",
            value: Math.round(firstInput.processingStart - firstInput.startTime),
          })
        }
      }).observe({ entryTypes: ["first-input"] })
    }

    // Start measuring after a short delay to avoid interfering with initial render
    // const timeoutId = setTimeout(measureWebVitals, 1000)

    // return () => clearTimeout(timeoutId)
    */ // End of commented performance monitoring code
  }, [hasInitialData, hasRealtimeSync])

  // Log performance metrics in development
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("Dashboard Performance:", {
        hasInitialData,
        hasRealtimeSync,
        timestamp: performance.now(),
      })
    }
  }, [hasInitialData, hasRealtimeSync])

  return null
}

// Extend window type for TypeScript
declare global {
  interface Window {
    dashboardMetrics?: {
      initialDataTime?: number
      realtimeSyncTime?: number
    }
    gtag?: (...args: any[]) => void
  }
}
