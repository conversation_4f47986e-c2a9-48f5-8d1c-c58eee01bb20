import { redirect } from "next/navigation"
import { AuthServerService } from "@/lib/server/auth/auth.service"
import { DashboardServerService } from "@/lib/server/domains/dashboard/dashboard.service"
import { DashboardClient } from "./components/DashboardClient"

/**
 * Server Component for Dashboard Page
 * Fetches all data server-side for optimal performance
 */
export default async function DashboardPage() {
  // Get authenticated user
  const user = await AuthServerService.getServerUser()

  if (!user) {
    redirect("/login")
  }

  // Check if user is new and should be redirected to welcome
  if (user.newUser === true) {
    redirect("/welcome")
  }

  try {
    // Fetch all dashboard data in parallel on the server
    const dashboardData = await DashboardServerService.getDashboardData(user.uid)

    return <DashboardClient initialData={dashboardData} user={user} />
  } catch (error) {
    console.error("Error loading dashboard data:", error)

    // Fallback to lightweight data if full data fetch fails
    try {
      const lightweightData = await DashboardServerService.getLightweightDashboardData(user.uid)

      return <DashboardClient initialData={lightweightData} user={user} hasError={true} />
    } catch (fallbackError) {
      console.error("Error loading lightweight dashboard data:", fallbackError)

      // Ultimate fallback - redirect to error page or show minimal UI
      redirect("/error?message=dashboard-load-failed")
    }
  }
}
