import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    // Create response
    const response = NextResponse.json({
      success: true,
      message: "Logged out successfully",
    })

    // Clear the HttpOnly auth cookie
    const cookieOptions = [
      "firebase-auth-token=",
      "Max-Age=0",
      "Path=/",
      "SameSite=Lax",
      "HttpOnly",
    ]

    // Add Secure flag in production
    if (process.env.NODE_ENV === "production") {
      cookieOptions.push("Secure")
    }

    response.headers.set("Set-Cookie", cookieOptions.join("; "))

    return response
  } catch (error: any) {
    console.error("Logout API error:", error)
    return NextResponse.json(
      {
        error: error.message || "An error occurred during logout",
      },
      { status: 500 }
    )
  }
}
