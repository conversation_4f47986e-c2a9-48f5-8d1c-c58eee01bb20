import { type NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"

export async function POST(request: NextRequest) {
  try {
    // Get the session cookie to revoke it
    const sessionCookie = request.cookies.get("firebase-auth-token")?.value

    if (sessionCookie) {
      try {
        // Get Firebase Admin instance
        const { adminAuth } = await getAdminInstance()

        if (adminAuth) {
          // Verify and revoke the session cookie
          const decodedToken = await adminAuth.verifySessionCookie(sessionCookie, true)
          await adminAuth.revokeRefreshTokens(decodedToken.uid)
        }
      } catch (error) {
        // Log error but don't fail the logout - cookie will still be cleared
        console.warn("Error revoking session cookie:", error)
      }
    }

    // Create response
    const response = NextResponse.json({
      success: true,
      message: "Logged out successfully",
    })

    // Clear the HttpOnly auth cookie
    const cookieOptions = [
      "firebase-auth-token=",
      "Max-Age=0",
      "Path=/",
      "SameSite=Lax",
      "HttpOnly",
    ]

    // Add Secure flag in production
    if (process.env.NODE_ENV === "production") {
      cookieOptions.push("Secure")
    }

    response.headers.set("Set-Cookie", cookieOptions.join("; "))

    return response
  } catch (error: any) {
    console.error("Logout API error:", error)
    return NextResponse.json(
      {
        error: error.message || "An error occurred during logout",
      },
      { status: 500 }
    )
  }
}
