"use client"

import { User } from "firebase/auth"

/**
 * Client-side service to manage auth cookies for SSR
 */
export class AuthCookieService {
  /**
   * Set Firebase token in cookie for server-side access
   */
  static async setAuthCookie(user: User | null) {
    try {
      if (user) {
        const token = await user.getIdToken()
        
        // Set cookie via document.cookie (will be picked up by middleware)
        document.cookie = `firebase-token=${token}; path=/; max-age=${60 * 60 * 24 * 7}; samesite=lax${
          process.env.NODE_ENV === "production" ? "; secure" : ""
        }`
      } else {
        // Clear cookie on logout
        document.cookie = "firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
      }
    } catch (error) {
      console.error("Error setting auth cookie:", error)
    }
  }

  /**
   * Clear auth cookie
   */
  static clearAuthCookie() {
    document.cookie = "firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
  }

  /**
   * Refresh auth cookie with new token
   */
  static async refreshAuthCookie(user: User) {
    try {
      const token = await user.getIdToken(true) // Force refresh
      document.cookie = `firebase-token=${token}; path=/; max-age=${60 * 60 * 24 * 7}; samesite=lax${
        process.env.NODE_ENV === "production" ? "; secure" : ""
      }`
    } catch (error) {
      console.error("Error refreshing auth cookie:", error)
    }
  }
}
