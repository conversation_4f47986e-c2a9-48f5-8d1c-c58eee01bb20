import { cookies } from "next/headers"
import { getAdminInstance } from "@/lib/firebase-admin"

export interface ServerUser {
  uid: string
  email?: string
  displayName?: string
  photoURL?: string
}

/**
 * Get the authenticated user on the server side
 * This function extracts the Firebase ID token from cookies and verifies it
 */
export async function getServerUser(): Promise<ServerUser | null> {
  try {
    // Get the auth token from cookies
    const cookieStore = await cookies()
    const authToken = cookieStore.get("firebase-auth-token")?.value

    if (!authToken) {
      return null
    }

    // Initialize Firebase Admin if needed
    const { adminAuth } = await getAdminInstance()

    if (!adminAuth) {
      console.error("Firebase Admin Auth is not initialized")
      return null
    }

    // Verify the session cookie
    const decodedToken = await adminAuth.verifySessionCookie(authToken, true)

    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      displayName: decodedToken.name,
      photoURL: decodedToken.picture,
    }
  } catch (error) {
    // If auth token doesn't exist or is invalid
    console.warn("Auth token verification failed:", error)
    return null
  }
}

/**
 * Alternative method to get user from request headers (for API routes)
 * This is used when we have the Firebase ID token in headers
 */
export async function getUserFromHeaders(headers: Headers): Promise<ServerUser | null> {
  try {
    const authHeader = headers.get("Authorization")

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null
    }

    const token = authHeader.split("Bearer ")[1]

    // Initialize Firebase Admin if needed
    const { adminAuth } = await getAdminInstance()

    if (!adminAuth) {
      console.error("Firebase Admin Auth is not initialized")
      return null
    }

    // Verify the ID token (this function still uses ID tokens from headers)
    const decodedToken = await adminAuth.verifyIdToken(token)

    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      displayName: decodedToken.name,
      photoURL: decodedToken.picture,
    }
  } catch (error) {
    console.error("Error verifying token from headers:", error)
    return null
  }
}
