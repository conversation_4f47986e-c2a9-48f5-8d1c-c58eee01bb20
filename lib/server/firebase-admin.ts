import { initializeApp, cert, getApps, getApp } from "firebase-admin/app"
import { getAuth } from "firebase-admin/auth"
import { getFirestore, FieldValue } from "firebase-admin/firestore"

const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY as string)

// Define a module-level variable to hold the initialized app instance.
let firebaseApp: ReturnType<typeof initializeApp> | undefined

// Use a function to ensure the app is only initialized once.
function getAdminApp() {
  if (getApps().length === 0) {
    firebaseApp = initializeApp({
      credential: cert(serviceAccount),
    })
  } else {
    firebaseApp = getApp()
  }
  return firebaseApp
}

// Export the instances directly, using the getAdminApp() function.
export const adminAuth = getAuth(getAdminApp())
export const adminDb = getFirestore(getAdminApp())
export const adminFieldValue = FieldValue
