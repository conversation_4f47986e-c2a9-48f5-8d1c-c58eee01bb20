import { Squad } from "@/lib/domains/squad/squad.types"
import { <PERSON> } from "@/lib/domains/trip/trip.types"
import { User } from "@/lib/domains/user/user.types"
import { UserServerService } from "../user/user.service"
import { SquadServerService } from "../squad/squad.service"
import { TripServerService } from "../trip/trip.service"

/**
 * Dashboard data structure for SSR
 */
export interface DashboardData {
  user: User
  squads: Squad[]
  upcomingTrips: Trip[]
  pastTrips: Trip[]
  squadLeaders: Record<string, User>
  tripsAttendeesDetails: Record<string, any[]>
  demoTourOptedOut: boolean
}

/**
 * Server-side Dashboard Service for SSR data fetching
 */
export class DashboardServerService {
  /**
   * Get all dashboard data in parallel for optimal performance
   * @param userId User ID
   * @returns Complete dashboard data
   */
  static async getDashboardData(userId: string): Promise<DashboardData> {
    try {
      // Phase 1: Get user data and basic entities in parallel
      const [user, squads, demoTourOptedOut] = await Promise.allSettled([
        UserServerService.getUser(userId),
        SquadServerService.getUserSquads(userId),
        UserServerService.hasDemoTourOptedOut(userId),
      ])

      // Handle potential failures
      const userData = user.status === "fulfilled" ? user.value : null
      const squadsData = squads.status === "fulfilled" ? squads.value : []
      const demoOptedOut = demoTourOptedOut.status === "fulfilled" ? demoTourOptedOut.value : false

      if (!userData) {
        throw new Error("User not found")
      }

      // Phase 2: Temporarily skip trips to get basic dashboard working
      // TODO: Re-enable after fixing TripServerService
      const upcomingTrips: Trip[] = []
      const pastTrips: Trip[] = []

      // Phase 3: Get additional details in parallel
      const leaderIds = squadsData.map((squad) => squad.leaderId).filter(Boolean)
      const upcomingTripIds = upcomingTrips.map((trip) => trip.id)

      const [squadLeadersResult, tripsAttendeesResult] = await Promise.allSettled([
        UserServerService.getUsers(leaderIds),
        this.getTripsAttendeesDetails(upcomingTripIds),
      ])

      const squadLeaders = squadLeadersResult.status === "fulfilled" ? squadLeadersResult.value : {}
      const tripsAttendeesDetails =
        tripsAttendeesResult.status === "fulfilled" ? tripsAttendeesResult.value : {}

      return {
        user: userData,
        squads: squadsData,
        upcomingTrips,
        pastTrips,
        squadLeaders,
        tripsAttendeesDetails,
        demoTourOptedOut: demoOptedOut,
      }
    } catch (error) {
      console.error("Error getting dashboard data (server):", error)
      throw error
    }
  }

  /**
   * Get attendees details for multiple trips
   * @param tripIds Array of trip IDs
   * @returns Record of trip ID to attendees with user details
   */
  private static async getTripsAttendeesDetails(tripIds: string[]): Promise<Record<string, any[]>> {
    try {
      if (tripIds.length === 0) return {}

      // Get attendees for all trips in parallel
      const attendeesPromises = tripIds.map((tripId) =>
        TripServerService.getTripAttendeesWithDetails(tripId)
      )

      const attendeesResults = await Promise.allSettled(attendeesPromises)

      const tripsAttendeesDetails: Record<string, any[]> = {}

      attendeesResults.forEach((result, index) => {
        const tripId = tripIds[index]
        if (result.status === "fulfilled") {
          tripsAttendeesDetails[tripId] = result.value
        } else {
          tripsAttendeesDetails[tripId] = []
        }
      })

      return tripsAttendeesDetails
    } catch (error) {
      console.error("Error getting trips attendees details (server):", error)
      return {}
    }
  }

  /**
   * Get lightweight dashboard data for faster loading
   * @param userId User ID
   * @returns Essential dashboard data only
   */
  static async getLightweightDashboardData(userId: string): Promise<Partial<DashboardData>> {
    try {
      // Initialize Firebase Admin if not already done

      const [user, squads] = await Promise.allSettled([
        UserServerService.getUser(userId),
        SquadServerService.getUserSquads(userId),
      ])

      const userData = user.status === "fulfilled" ? user.value : null
      const squadsData = squads.status === "fulfilled" ? squads.value : []

      if (!userData) {
        throw new Error("User not found")
      }

      return {
        user: userData,
        squads: squadsData,
        upcomingTrips: [],
        pastTrips: [],
        squadLeaders: {},
        tripsAttendeesDetails: {},
        demoTourOptedOut: false,
      }
    } catch (error) {
      console.error("Error getting lightweight dashboard data (server):", error)
      throw error
    }
  }
}
