import { Timestamp } from "firebase-admin/firestore"
import { User } from "@/lib/domains/user/user.types"
import { getAdminInstance } from "@/lib/firebase-admin"

/**
 * Server-side User Service using Firebase Admin SDK
 */
export class UserServerService {
  /**
   * Serialize Firestore Timestamps to plain objects for SSR
   */
  private static serializeTimestamps(data: any): any {
    if (!data) return data

    if (data instanceof Timestamp) {
      return data.toDate()
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.serializeTimestamps(item))
    }

    if (typeof data === "object" && data !== null) {
      const serialized: any = {}
      for (const [key, value] of Object.entries(data)) {
        serialized[key] = this.serializeTimestamps(value)
      }
      return serialized
    }

    return data
  }
  private static readonly COLLECTION = "users"

  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async getUser(userId: string): Promise<User | null> {
    try {
      const { adminDb } = await getAdminInstance()
      const userDoc = await adminDb.collection(this.COLLECTION).doc(userId).get()

      if (userDoc.exists) {
        const userData = userDoc.data()
        // Convert Firestore Timestamps to plain objects for SSR
        const serializedData = this.serializeTimestamps(userData)
        return { ...serializedData, uid: userId } as User
      }

      return null
    } catch (error) {
      console.error("Error getting user (server):", error)
      throw error
    }
  }

  /**
   * Get multiple users by IDs
   * @param userIds Array of user IDs
   * @returns Record of user ID to user data
   */
  static async getUsers(userIds: string[]): Promise<Record<string, User>> {
    try {
      if (userIds.length === 0) return {}

      const { adminDb } = await getAdminInstance()
      const users: Record<string, User> = {}

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      const batchSize = 10
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize)
        const userDocs = await Promise.all(
          batch.map((userId) => adminDb.collection(this.COLLECTION).doc(userId).get())
        )

        userDocs.forEach((doc: any, index: number) => {
          if (doc.exists) {
            const userData = doc.data()
            const serializedData = this.serializeTimestamps(userData)
            const userId = batch[index]
            users[userId] = { ...serializedData, uid: userId } as User
          }
        })
      }

      return users
    } catch (error) {
      console.error("Error getting users (server):", error)
      throw error
    }
  }

  /**
   * Check if user is new
   * @param userId User ID
   * @returns True if user is new, false otherwise
   */
  static async isNewUser(userId: string): Promise<boolean> {
    try {
      const user = await this.getUser(userId)
      return user?.newUser === true
    } catch (error) {
      console.error("Error checking if user is new (server):", error)
      return false
    }
  }

  /**
   * Check if user has opted out of demo tour
   * @param userId User ID
   * @returns True if user has opted out, false otherwise
   */
  static async hasDemoTourOptedOut(userId: string): Promise<boolean> {
    try {
      const user = await this.getUser(userId)
      return user?.demoTourOptedOut === true
    } catch (error) {
      console.error("Error checking demo tour opt-out (server):", error)
      return false
    }
  }
}
